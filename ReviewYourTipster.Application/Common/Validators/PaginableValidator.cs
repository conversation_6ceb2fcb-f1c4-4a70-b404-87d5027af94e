using FluentValidation;

namespace ReviewYourTipster.Application.Common.Validators;

public class PaginableValidator : AbstractValidator<IPaginable>
{
    public PaginableValidator()
    {
        RuleFor(x => x.Page)
            .NotEmpty().WithErrorCode("PageRequired").WithMessage("Page is required")
            .GreaterThanOrEqualTo(1).WithErrorCode("PageOutOfRange")
            .WithMessage("Page must be greater than or equal to 1");

        RuleFor(x => x.PageSize)
            .NotEmpty().WithErrorCode("PageSizeRequired").WithMessage("PageSize is required")
            .LessThanOrEqualTo(100).WithErrorCode("PageSizeOutOfRange")
            .WithMessage("PageSize must be less than or equal to 100");
    }
}